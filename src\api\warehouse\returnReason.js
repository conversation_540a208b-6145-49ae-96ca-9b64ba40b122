import request from '@/utils/request'

// 查询退货原因分类列表
export function listReturnReason(query) {
  return request({
    url: '/warehouse/returnReason/list',
    method: 'get',
    params: query
  })
}

// 查询退货原因分类详细
export function getReturnReason(reasonId) {
  return request({
    url: '/warehouse/returnReason/' + reasonId,
    method: 'get'
  })
}

// 新增退货原因分类
export function addReturnReason(data) {
  return request({
    url: '/warehouse/returnReason',
    method: 'post',
    data: data
  })
}

// 修改退货原因分类
export function updateReturnReason(data) {
  return request({
    url: '/warehouse/returnReason',
    method: 'put',
    data: data
  })
}

// 删除退货原因分类
export function delReturnReason(reasonId) {
  return request({
    url: '/warehouse/returnReason/' + reasonId,
    method: 'delete'
  })
}

// 校验原因编码是否唯一
export function checkReasonCodeUnique(data) {
  return request({
    url: '/warehouse/returnReason/checkReasonCodeUnique',
    method: 'post',
    data: data
  })
}

// 校验原因名称是否唯一
export function checkReasonNameUnique(data) {
  return request({
    url: '/warehouse/returnReason/checkReasonNameUnique',
    method: 'post',
    data: data
  })
} 