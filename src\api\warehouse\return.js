import request from '@/utils/request'

// 查询退货单列表
export function listReturn(query) {
  return request({
    url: '/warehouse/return/list',
    method: 'get',
    params: query
  })
}

// 查询退货单详细
export function getReturn(returnId) {
  return request({
    url: '/warehouse/return/' + returnId,
    method: 'get'
  })
}

// 新增退货单
export function addReturn(data) {
  return request({
    url: '/warehouse/return',
    method: 'post',
    data: data
  })
}

// 修改退货单
export function updateReturn(data) {
  return request({
    url: '/warehouse/return',
    method: 'put',
    data: data
  })
}

// 删除退货单
export function delReturn(returnId) {
  return request({
    url: '/warehouse/return/' + returnId,
    method: 'delete'
  })
}

// 导出退货单
export function exportReturn(query) {
  return request({
    url: '/warehouse/return/export',
    method: 'post',
    params: query
  })
}

// 获取退货原因选项列表
export function getReturnReasonOptions() {
  return request({
    url: '/warehouse/returnReason/options',
    method: 'get'
  })
} 