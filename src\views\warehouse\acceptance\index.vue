<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="物资名称" prop="itemName">
        <el-input v-model="queryParams.itemName" placeholder="请输入物资名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="供应商" prop="supplierName">
        <el-input v-model="queryParams.supplierName" placeholder="请输入供应商名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="验收状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="验收状态" clearable>
          <el-option label="全部" value="" />
          <el-option label="合格" value="qualified" />
          <el-option label="不合格" value="unqualified" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增验收</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-upload" size="mini" @click="handleBatchImport">批量导入</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 验收入库表格 -->
    <el-table v-if="refreshTable" v-loading="loading" :data="acceptanceList" row-key="acceptanceId">
      <el-table-column prop="itemName" label="物资名称" width="180" />
      <el-table-column prop="specification" label="规格型号" width="120" />
      <el-table-column prop="quantity" label="数量" width="80" />
      <el-table-column prop="supplierName" label="供应商" width="150" />
      <el-table-column prop="voucherNo" label="凭证号" width="120" />
      <el-table-column prop="status" label="验收状态" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 'qualified'" type="success">合格</el-tag>
          <el-tag v-else-if="scope.row.status === 'unqualified'" type="danger">不合格</el-tag>
          <el-tag v-else>待验收</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="images" label="入库图片" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.images" class="image-preview">
            <el-image
              v-for="(img, index) in getImageList(scope.row.images)"
              :key="index"
              :src="img"
              :preview-src-list="getImageList(scope.row.images)"
              style="width: 30px; height: 30px; margin-right: 5px; cursor: pointer;"
              fit="cover"
              class="preview-image">
            </el-image>
          </div>
          <span v-else>无图片</span>
        </template>
      </el-table-column>
      <el-table-column prop="acceptanceTime" label="验收时间" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.acceptanceTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-check" v-if="scope.row.status !== 'qualified'" @click="markQualified(scope.row)">合格入库</el-button>
          <el-button size="mini" type="text" icon="el-icon-close" v-if="scope.row.status !== 'unqualified'" @click="markUnqualified(scope.row)">不合格退货</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑验收入库弹窗 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="物资名称" prop="itemName">
              <el-input v-model="form.itemName" placeholder="请输入物资名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格型号" prop="specification">
              <el-input v-model="form.specification" placeholder="请输入规格型号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="数量" prop="quantity">
              <el-input-number v-model="form.quantity" :min="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplierId">
              <el-select v-model="form.supplierId" placeholder="请选择供应商">
                <el-option v-for="s in supplierOptions" :key="s.supplierId" :label="s.supplierName" :value="s.supplierId" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="凭证号" prop="voucherNo">
              <el-input v-model="form.voucherNo" placeholder="请输入凭证号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="验收状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="qualified">合格</el-radio>
                <el-radio label="unqualified">不合格</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="入库图片">
              <image-upload-multiple v-model="form.images" :limit="5" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listAcceptance,
  getAcceptance,
  addAcceptance,
  updateAcceptance,
  delAcceptance,
  markQualified,
  markUnqualified
} from '@/api/warehouse/acceptance'
import { getSupplierOptions } from '@/api/warehouse/supplier'
import ImageUploadMultiple from '@/components/ImageUploadMultiple'

export default {
  name: "Acceptance",
  components: {
    ImageUploadMultiple
  },
  data() {
    return {
      loading: false,
      showSearch: true,
      refreshTable: true,
      acceptanceList: [],
      supplierOptions: [],
      title: "",
      open: false,
      queryParams: {
        itemName: undefined,
        supplierName: undefined,
        status: undefined
      },
      form: {},
      rules: {
        itemName: [ { required: true, message: "物资名称不能为空", trigger: "blur" } ],
        specification: [ { required: true, message: "规格型号不能为空", trigger: "blur" } ],
        quantity: [ { required: true, message: "数量不能为空", trigger: "blur" } ],
        supplierId: [ { required: true, message: "请选择供应商", trigger: "change" } ],
        voucherNo: [ { required: true, message: "凭证号不能为空", trigger: "blur" } ]
      }
    };
  },
  created() {
    this.getList();
    this.getSupplierOptions();
  },
  methods: {
    parseTime(time) {
      if (!time) return '';
      const d = new Date(time);
      return d.toLocaleString();
    },
    getImageList(images) {
      if (!images) return [];
      return images.split(',').filter(img => img.trim() !== '');
    },
    getList() {
      this.loading = true;
      listAcceptance(this.queryParams).then(res => {
        this.acceptanceList = res.rows;
        this.loading = false;
      });
    },
    getSupplierOptions() {
      getSupplierOptions().then(res => {
        this.supplierOptions = res.data;
      }).catch(() => {
        this.supplierOptions = [];
      });
    },
    handleQuery() {
      this.getList();
    },
    resetQuery() {
      this.queryParams = { itemName: undefined, supplierName: undefined, status: undefined };
      this.handleQuery();
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增验收入库";
    },
    handleUpdate(row) {
      getAcceptance(row.acceptanceId).then(res => {
        this.form = Object.assign({}, res.data);
        this.open = true;
        this.title = "编辑验收入库";
      });
    },
    handleDelete(row) {
      this.$confirm('是否确认删除物资"' + row.itemName + '"的验收记录？', '提示', { type: 'warning' })
        .then(() => {
          return delAcceptance(row.acceptanceId)
        })
        .then(() => {
          this.$message.success('删除成功');
          this.getList();
        })
        .catch(() => {});
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.acceptanceId) {
            updateAcceptance(this.form).then(() => {
              this.$message.success("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAcceptance(this.form).then(() => {
              this.$message.success("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        acceptanceId: undefined,
        itemName: undefined,
        specification: undefined,
        quantity: 1,
        supplierId: undefined,
        voucherNo: undefined,
        status: 'qualified',
        images: undefined,
        remark: ''
      };
      if (this.$refs.form) this.$refs.form.resetFields();
    },
    markQualified(row) {
      markQualified(row.acceptanceId).then(() => {
        this.$message.success('物资"' + row.itemName + '"已合格入库，库存已同步');
        this.getList();
      });
    },
    markUnqualified(row) {
      markUnqualified(row.acceptanceId).then(() => {
        this.$message.warning('物资"' + row.itemName + '"不合格，已触发退货流程');
        this.getList();
      });
    },
    handleBatchImport() {
      this.$message.info('批量导入功能开发中...');
    }
  }
};
</script>

<style scoped>
.mb8 { margin-bottom: 8px; }
.mt10 { margin-top: 10px; }

.image-preview {
  display: flex;
  flex-wrap: wrap;
}

.preview-image {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}
</style> 