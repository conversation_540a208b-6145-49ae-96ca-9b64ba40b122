<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="采购单号" prop="purchaseCode">
        <el-input
          v-model="queryParams.purchaseCode"
          placeholder="请输入采购单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供应单位" prop="supplierId">
        <el-select v-model="queryParams.supplierId" placeholder="请选择供应单位" clearable>
          <el-option
            v-for="supplier in supplierOptions"
            :key="supplier.supplierId"
            :label="supplier.supplierName"
            :value="supplier.supplierId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请项目" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入申请项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="applicant">
        <el-input
          v-model="queryParams.applicant"
          placeholder="请输入申请人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="配件名称" prop="partName">
        <el-input
          v-model="queryParams.partName"
          placeholder="请输入配件名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="采购状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择采购状态" clearable>
          <el-option label="待审核" value="0" />
          <el-option label="已审核" value="1" />
          <el-option label="采购中" value="2" />
          <el-option label="已完成" value="3" />
          <el-option label="已取消" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="采购日期" prop="purchaseDate">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['warehouse:purchase:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['warehouse:purchase:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['warehouse:purchase:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['warehouse:purchase:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['warehouse:purchase:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-document"
          size="mini"
          @click="handleMonthlyReport"
          v-hasPermi="['warehouse:purchase:report']"
        >月度报表</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-document"
          size="mini"
          @click="handleYearlyReport"
          v-hasPermi="['warehouse:purchase:report']"
        >年度报表</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="purchaseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="采购单号" align="center" prop="purchaseCode" />
      <el-table-column label="供应单位" align="center" prop="supplierName" />
      <el-table-column label="申请项目" align="center" prop="projectName" />
      <el-table-column label="申请人" align="center" prop="applicant" />
      <el-table-column label="配件名称" align="center" prop="partName" />
              <el-table-column label="配件属性" align="center" prop="partAttribute">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.part_attribute" :value="scope.row.partAttribute"/>
          </template>
        </el-table-column>
      <el-table-column label="数量" align="center" prop="quantity" />
      <el-table-column label="单价" align="center" prop="unitPrice">
        <template slot-scope="scope">
          ¥{{ scope.row.unitPrice || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="总金额" align="center" prop="totalAmount">
        <template slot-scope="scope">
          ¥{{ scope.row.totalAmount || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="采购状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.purchase_status" :value="scope.row.status || '0'"/>
        </template>
      </el-table-column>
      <el-table-column label="采购日期" align="center" prop="purchaseDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.purchaseDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['warehouse:purchase:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['warehouse:purchase:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="采购单号" prop="purchaseCode">
              <el-input v-model="form.purchaseCode" placeholder="请输入采购单号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应单位" prop="supplierId">
              <el-select v-model="form.supplierId" placeholder="请选择供应单位">
                <el-option
                  v-for="supplier in supplierOptions"
                  :key="supplier.supplierId"
                  :label="supplier.supplierName"
                  :value="supplier.supplierId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="申请项目" prop="projectName">
              <el-input v-model="form.projectName" placeholder="请输入申请项目" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicant">
              <el-input v-model="form.applicant" placeholder="请输入申请人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="配件名称" prop="partName">
              <el-input v-model="form.partName" placeholder="请输入配件名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="配件属性" prop="partAttribute">
              <el-select v-model="form.partAttribute" placeholder="请选择配件属性">
                <el-option
                  v-for="dict in dict.type.part_attribute"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="数量" prop="quantity">
              <el-input-number v-model="form.quantity" :min="1" placeholder="请输入数量" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单价" prop="unitPrice">
              <el-input-number v-model="form.unitPrice" :min="0" :precision="2" placeholder="请输入单价" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="采购日期" prop="purchaseDate">
              <el-date-picker
                v-model="form.purchaseDate"
                type="date"
                placeholder="选择采购日期"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择采购状态">
                <el-option label="待审核" value="0" />
                <el-option label="已审核" value="1" />
                <el-option label="采购中" value="2" />
                <el-option label="已完成" value="3" />
                <el-option label="已取消" value="4" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-error="handleFileError"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />
            是否更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPurchase, getPurchase, delPurchase, addPurchase, updatePurchase, exportPurchase, generateMonthlyReport, generateYearlyReport, getSupplierOptions } from "@/api/warehouse/purchase";
import { getToken } from "@/utils/auth";

export default {
  name: "Purchase",
  dicts: ['purchase_status', 'part_attribute'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购单表格数据
      purchaseList: [],
      // 供应商选项
      supplierOptions: [],
      // 日期范围
      dateRange: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        purchaseCode: undefined,
        supplierId: undefined,
        projectName: undefined,
        applicant: undefined,
        partName: undefined,
        status: undefined
      },
      // 表单参数
      form: {
        purchaseId: undefined,
        purchaseCode: undefined,
        supplierId: undefined,
        projectName: undefined,
        applicant: undefined,
        partName: undefined,
        partAttribute: undefined,
        quantity: 1,
        unitPrice: 0,
        totalAmount: 0,
        purchaseDate: undefined,
        status: "0",
        remark: undefined
      },
      // 表单校验
      rules: {
        purchaseCode: [
          { required: true, message: "采购单号不能为空", trigger: "blur" }
        ],
        supplierId: [
          { required: true, message: "供应单位不能为空", trigger: "change" }
        ],
        projectName: [
          { required: true, message: "申请项目不能为空", trigger: "blur" }
        ],
        applicant: [
          { required: true, message: "申请人不能为空", trigger: "blur" }
        ],
        partName: [
          { required: true, message: "配件名称不能为空", trigger: "blur" }
        ],
        quantity: [
          { required: true, message: "数量不能为空", trigger: "blur" }
        ],
        unitPrice: [
          { required: true, message: "单价不能为空", trigger: "blur" }
        ]
      },
      // 文件上传相关
      upload: {
        open: false,
        title: '',
        isUploading: false,
        updateSupport: 0,
        headers: { Authorization: "Bearer " + getToken() },
        url: process.env.VUE_APP_BASE_API + "/warehouse/purchase/importData"
      }
    };
  },
  created() {
    this.getList();
    this.getSupplierOptions();
  },
  methods: {
    parseTime(time) {
      if (!time) return '';
      const d = new Date(time);
      return d.toLocaleString();
    },
    /** 查询采购单列表 */
    getList() {
      this.loading = true;
      listPurchase(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.purchaseList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
      }).catch(error => {
        console.error('获取采购单列表失败:', error);
        this.purchaseList = [];
        this.total = 0;
        this.loading = false;
      });
    },
    /** 获取供应商选项 */
    getSupplierOptions() {
      getSupplierOptions().then(response => {
        this.supplierOptions = response.data || [];
      }).catch(error => {
        console.error('获取供应商选项失败:', error);
        this.supplierOptions = [];
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        purchaseId: undefined,
        purchaseCode: undefined,
        supplierId: undefined,
        projectName: undefined,
        applicant: undefined,
        partName: undefined,
        partAttribute: undefined,
        quantity: 1,
        unitPrice: 0,
        totalAmount: 0,
        purchaseDate: undefined,
        status: "0",
        remark: undefined
      };
      if (this.$refs.form) this.$refs.form.resetFields();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      if (this.$refs.queryForm) this.$refs.queryForm.resetFields();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.purchaseId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采购单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const purchaseId = row.purchaseId || this.ids;
      getPurchase(purchaseId).then(response => {
        this.form = response.data || {};
        this.open = true;
        this.title = "修改采购单";
      }).catch(error => {
        console.error('获取采购单详情失败:', error);
        this.$message.error('获取采购单详情失败');
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 计算总金额
          const quantity = parseFloat(this.form.quantity) || 0;
          const unitPrice = parseFloat(this.form.unitPrice) || 0;
          this.form.totalAmount = quantity * unitPrice;
          
          if (this.form.purchaseId != undefined) {
            updatePurchase(this.form).then(response => {
              this.$message.success("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('修改采购单失败:', error);
              this.$message.error('修改失败');
            });
          } else {
            addPurchase(this.form).then(response => {
              this.$message.success("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.error('新增采购单失败:', error);
              this.$message.error('新增失败');
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const purchaseIds = row.purchaseId || this.ids;
      this.$confirm('是否确认删除采购单编号为"' + purchaseIds + '"的数据项？', '提示', { type: 'warning' }).then(function() {
        return delPurchase(purchaseIds);
      }).then(() => {
        this.getList();
        this.$message.success("删除成功");
      }).catch(error => {
        console.error('删除采购单失败:', error);
        this.$message.error('删除失败');
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$confirm('是否确认导出所有采购单数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.$message.info('正在导出数据，请稍候...');
        this.download('warehouse/purchase/export', {
          ...this.queryParams
        }, `purchase_${new Date().getTime()}.xlsx`)
      }).catch(error => {
        console.error('导出操作取消或失败:', error);
      });
    },
    /** 月度报表按钮操作 */
    handleMonthlyReport() {
      this.$confirm('是否确认生成月度采购报表?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      }).then(() => {
        this.$message.info('正在生成月度报表，请稍候...');
        this.download('warehouse/purchase/monthlyReport', {
          ...this.queryParams
        }, `purchase_monthly_report_${new Date().getTime()}.xlsx`)
      }).catch(error => {
        console.error('月度报表操作取消或失败:', error);
      });
    },
    /** 年度报表按钮操作 */
    handleYearlyReport() {
      this.$confirm('是否确认生成年度采购报表?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      }).then(() => {
        this.$message.info('正在生成年度报表，请稍候...');
        this.download('warehouse/purchase/yearlyReport', {
          ...this.queryParams
        }, `purchase_yearly_report_${new Date().getTime()}.xlsx`)
      }).catch(error => {
        console.error('年度报表操作取消或失败:', error);
      });
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "采购单导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('warehouse/purchase/importTemplate', {
      }, `purchase_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$message.success(response.msg || '导入成功');
      this.getList();
    },
    // 文件上传失败处理
    handleFileError(error, file, fileList) {
      this.upload.isUploading = false;
      console.error('文件上传失败:', error);
      this.$message.error('文件上传失败');
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script> 