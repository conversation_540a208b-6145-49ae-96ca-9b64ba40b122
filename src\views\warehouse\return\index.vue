<template>
  <div class="app-container">
    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="退货单号" prop="returnCode">
        <el-input
          v-model="queryParams.returnCode"
          placeholder="请输入退货单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="出库单号" prop="outboundCode">
        <el-input
          v-model="queryParams.outboundCode"
          placeholder="请输入出库单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="供应商" prop="supplierId">
        <el-select v-model="queryParams.supplierId" placeholder="请选择供应商" clearable>
          <el-option
            v-for="supplier in supplierOptions"
            :key="supplier.supplierId"
            :label="supplier.supplierName"
            :value="supplier.supplierId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="退货原因" prop="returnReasonId">
        <el-select v-model="queryParams.returnReasonId" placeholder="请选择退货原因" clearable>
          <el-option
            v-for="reason in returnReasonOptions"
            :key="reason.reasonId"
            :label="reason.reasonName"
            :value="reason.reasonId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="退货状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择退货状态" clearable>
          <el-option
            v-for="dict in dict.type.return_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="退货日期" prop="returnDate">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['warehouse:return:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['warehouse:return:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['warehouse:return:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['warehouse:return:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 退货单列表 -->
    <el-table v-loading="loading" :data="returnList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="退货单号" align="center" prop="returnCode" width="140" />
      <el-table-column label="出库单号" align="center" prop="outboundCode" width="140" />
      <el-table-column label="供应商" align="center" prop="supplierName" :show-overflow-tooltip="true" />
      <el-table-column label="退货日期" align="center" prop="returnDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.returnDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="退货原因" align="center" prop="returnReasonName" />
      <el-table-column label="退货状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.return_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="审批状态" align="center" prop="approvalStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.approval_status" :value="scope.row.approvalStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)" v-hasPermi="['warehouse:return:query']">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['warehouse:return:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['warehouse:return:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改退货单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <!-- 基本信息 -->
        <el-card class="mb20">
          <div slot="header">
            <span>基本信息</span>
          </div>
          <el-row>
            <el-col :span="8">
              <el-form-item label="退货单号" prop="returnCode">
                <el-input v-model="form.returnCode" placeholder="请输入退货单号" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="出库单号" prop="outboundCode">
                <el-input v-model="form.outboundCode" placeholder="请输入出库单号" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="供应商" prop="supplierId">
                <el-select v-model="form.supplierId" placeholder="请选择供应商" style="width: 100%">
                  <el-option
                    v-for="supplier in supplierOptions"
                    :key="supplier.supplierId"
                    :label="supplier.supplierName"
                    :value="supplier.supplierId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="退货日期" prop="returnDate">
                <el-date-picker
                  v-model="form.returnDate"
                  type="date"
                  placeholder="选择日期"
                  value-format="yyyy-MM-dd"
                  style="width: 100%">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="退货原因" prop="returnReasonId">
                <el-select v-model="form.returnReasonId" placeholder="请选择退货原因" style="width: 100%">
                  <el-option
                    v-for="reason in returnReasonOptions"
                    :key="reason.reasonId"
                    :label="reason.reasonName"
                    :value="reason.reasonId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="退货状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择退货状态" style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.return_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="经办人" prop="handler">
                <el-input v-model="form.handler" placeholder="请输入经办人" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- Tab页签 -->
        <el-tabs v-model="activeTab" type="card">
          <!-- 退货物质明细 -->
          <el-tab-pane label="退货物质明细" name="detail">
            <el-card>
              <div slot="header">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddDetail">添加明细</el-button>
              </div>
              <el-table :data="form.returnDetails" style="width: 100%">
                <el-table-column label="序号" type="index" width="50" align="center" />
                <el-table-column label="物质名称" prop="materialName" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialName" placeholder="请输入物质名称" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="规格型号" prop="specification" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.specification" placeholder="请输入规格型号" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="退货数量" prop="quantity" width="120">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.quantity" :min="1" size="small" style="width: 100%" />
                  </template>
                </el-table-column>
                <el-table-column label="单价" prop="unitPrice" width="120">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.unitPrice" :precision="2" :min="0" size="small" style="width: 100%" />
                  </template>
                </el-table-column>
                <el-table-column label="退货金额" prop="totalAmount" width="120">
                  <template slot-scope="scope">
                    <span>{{ (scope.row.quantity * scope.row.unitPrice).toFixed(2) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="退货原因" prop="detailReason" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.detailReason" placeholder="请输入退货原因" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDeleteDetail(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-tab-pane>

          <!-- 复验要求 -->
          <el-tab-pane label="复验要求" name="inspection">
            <el-card>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="复验标准" prop="inspectionStandard">
                    <el-input v-model="form.inspectionStandard" type="textarea" rows="3" placeholder="请输入复验标准" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="复验要求" prop="inspectionRequirement">
                    <el-input v-model="form.inspectionRequirement" type="textarea" rows="3" placeholder="请输入复验要求" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="复验人员" prop="inspector">
                    <el-input v-model="form.inspector" placeholder="请输入复验人员" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="复验日期" prop="inspectionDate">
                    <el-date-picker
                      v-model="form.inspectionDate"
                      type="date"
                      placeholder="选择复验日期"
                      value-format="yyyy-MM-dd"
                      style="width: 100%">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="复验结果" prop="inspectionResult">
                    <el-select v-model="form.inspectionResult" placeholder="请选择复验结果" style="width: 100%">
                      <el-option label="合格" value="qualified" />
                      <el-option label="不合格" value="unqualified" />
                      <el-option label="待复验" value="pending" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </el-tab-pane>

          <!-- 审批节点 -->
          <el-tab-pane label="审批节点" name="approval">
            <el-card>
              <div slot="header">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddApproval">添加审批节点</el-button>
              </div>
              <el-table :data="form.approvalNodes" style="width: 100%">
                <el-table-column label="序号" type="index" width="50" align="center" />
                <el-table-column label="审批层级" prop="approvalLevel" width="120">
                  <template slot-scope="scope">
                    <el-input-number v-model="scope.row.approvalLevel" :min="1" size="small" style="width: 100%" />
                  </template>
                </el-table-column>
                <el-table-column label="审批人" prop="approver" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.approver" placeholder="请输入审批人" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="审批岗位" prop="approvalPosition" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.approvalPosition" placeholder="请输入审批岗位" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="审批状态" prop="approvalStatus" width="130">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.approvalStatus" placeholder="审批状态" size="small" style="width: 100%">
                      <el-option
                        v-for="dict in dict.type.approval_status"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="审批时间" prop="approvalTime" width="200">
                  <template slot-scope="scope">
                    <el-date-picker
                      v-model="scope.row.approvalTime"
                      type="datetime"
                      placeholder="选择时间"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      size="small"
                      style="width: 100%">
                    </el-date-picker>
                  </template>
                </el-table-column>
                <el-table-column label="审批意见" prop="approvalOpinion" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.approvalOpinion" placeholder="请输入审批意见" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDeleteApproval(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-tab-pane>
        </el-tabs>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" rows="3" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看退货单详情对话框 -->
    <el-dialog title="退货单详情" :visible.sync="viewOpen" width="1200px" append-to-body>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="退货单号">{{ viewForm.returnCode }}</el-descriptions-item>
        <el-descriptions-item label="出库单号">{{ viewForm.outboundCode }}</el-descriptions-item>
        <el-descriptions-item label="供应商">{{ viewForm.supplierName }}</el-descriptions-item>
        <el-descriptions-item label="退货日期">{{ parseTime(viewForm.returnDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="退货原因">{{ viewForm.returnReasonName }}</el-descriptions-item>
        <el-descriptions-item label="退货状态">
          <dict-tag :options="dict.type.return_status" :value="viewForm.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="经办人">{{ viewForm.handler }}</el-descriptions-item>
      </el-descriptions>

      <el-divider>退货物质明细</el-divider>
      <el-table :data="viewForm.returnDetails" style="width: 100%">
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="物质名称" prop="materialName" />
        <el-table-column label="规格型号" prop="specification" />
        <el-table-column label="退货数量" prop="quantity" />
        <el-table-column label="单价" prop="unitPrice" />
        <el-table-column label="退货金额" prop="totalAmount" />
        <el-table-column label="退货原因" prop="detailReason" />
      </el-table>

      <el-divider>复验要求</el-divider>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="复验标准">{{ viewForm.inspectionStandard }}</el-descriptions-item>
        <el-descriptions-item label="复验要求">{{ viewForm.inspectionRequirement }}</el-descriptions-item>
        <el-descriptions-item label="复验人员">{{ viewForm.inspector }}</el-descriptions-item>
        <el-descriptions-item label="复验日期">{{ parseTime(viewForm.inspectionDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="复验结果">{{ viewForm.inspectionResult }}</el-descriptions-item>
      </el-descriptions>

      <el-divider>审批节点</el-divider>
      <el-table :data="viewForm.approvalNodes" style="width: 100%">
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="审批层级" prop="approvalLevel" width="100" align="center" />
        <el-table-column label="审批人" prop="approver" min-width="120" />
        <el-table-column label="审批岗位" prop="approvalPosition" min-width="120" />
        <el-table-column label="审批状态" prop="approvalStatus" width="100" align="center">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.approval_status" :value="scope.row.approvalStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="审批时间" prop="approvalTime" width="180" align="center">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.approvalTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审批意见" prop="approvalOpinion" min-width="150" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { listReturn, getReturn, delReturn, addReturn, updateReturn, exportReturn, getReturnReasonOptions } from "@/api/warehouse/return"
import { listSupplier } from "@/api/warehouse/supplier"

export default {
  name: "Return",
  dicts: ['return_status', 'approval_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 退货单表格数据
      returnList: [],
      // 供应商选项
      supplierOptions: [],
      // 退货原因选项
      returnReasonOptions: [],
      // 日期范围
      dateRange: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 当前活动的tab页签
      activeTab: "detail",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        returnCode: undefined,
        outboundCode: undefined,
        supplierId: undefined,
        returnReasonId: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 查看表单参数
      viewForm: {},
      // 表单校验
      rules: {
        returnCode: [
          { required: true, message: "退货单号不能为空", trigger: "blur" }
        ],
        supplierId: [
          { required: true, message: "供应商不能为空", trigger: "change" }
        ],
        returnDate: [
          { required: true, message: "退货日期不能为空", trigger: "blur" }
        ],
        returnReasonId: [
          { required: true, message: "退货原因不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getSupplierOptions();
    this.getReturnReasonOptions();
  },
  methods: {
    /** 查询退货单列表 */
    getList() {
      this.loading = true;
      listReturn(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.returnList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取供应商选项 */
    getSupplierOptions() {
      listSupplier({}).then(response => {
        this.supplierOptions = response.rows;
      });
    },
    /** 获取退货原因选项 */
    getReturnReasonOptions() {
      getReturnReasonOptions().then(response => {
        this.returnReasonOptions = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        returnId: undefined,
        returnCode: undefined,
        outboundCode: undefined,
        supplierId: undefined,
        supplierName: undefined,
        returnDate: undefined,
        returnReasonId: undefined,
        status: "0",
        handler: undefined,
        inspectionStandard: undefined,
        inspectionRequirement: undefined,
        inspector: undefined,
        inspectionDate: undefined,
        inspectionResult: undefined,
        approvalStatus: "0",
        remark: undefined,
        returnDetails: [],
        approvalNodes: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.returnId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加退货单";
      this.activeTab = "detail";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const returnId = row.returnId || this.ids;
      getReturn(returnId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改退货单";
        this.activeTab = "detail";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      const returnId = row.returnId;
      getReturn(returnId).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.returnId != null) {
            updateReturn(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReturn(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const returnIds = row.returnId || this.ids;
      this.$modal.confirm('是否确认删除退货单编号为"' + returnIds + '"的数据项？').then(function() {
        return delReturn(returnIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('warehouse/return/export', {
        ...this.queryParams
      }, `return_${new Date().getTime()}.xlsx`)
    },
    /** 添加退货明细 */
    handleAddDetail() {
      const newDetail = {
        materialName: "",
        specification: "",
        quantity: 1,
        unitPrice: 0,
        detailReason: ""
      };
      this.form.returnDetails.push(newDetail);
    },
    /** 删除退货明细 */
    handleDeleteDetail(index) {
      this.form.returnDetails.splice(index, 1);
    },
    /** 添加审批节点 */
    handleAddApproval() {
      const newApproval = {
        approvalLevel: this.form.approvalNodes.length + 1,
        approver: "",
        approvalPosition: "",
        approvalStatus: "0",
        approvalTime: undefined,
        approvalOpinion: ""
      };
      this.form.approvalNodes.push(newApproval);
    },
    /** 删除审批节点 */
    handleDeleteApproval(index) {
      this.form.approvalNodes.splice(index, 1);
      // 重新排序审批层级
      this.form.approvalNodes.forEach((item, idx) => {
        item.approvalLevel = idx + 1;
      });
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}
</style> 