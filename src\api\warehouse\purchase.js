import request from '@/utils/request'

// 查询采购单列表
export function listPurchase(query) {
  return request({
    url: '/warehouse/purchase/list',
    method: 'get',
    params: query
  })
}

// 查询采购单详细
export function getPurchase(purchaseId) {
  return request({
    url: '/warehouse/purchase/' + purchaseId,
    method: 'get'
  })
}

// 新增采购单
export function addPurchase(data) {
  return request({
    url: '/warehouse/purchase',
    method: 'post',
    data: data
  })
}

// 修改采购单
export function updatePurchase(data) {
  return request({
    url: '/warehouse/purchase',
    method: 'put',
    data: data
  })
}

// 删除采购单
export function delPurchase(purchaseId) {
  return request({
    url: '/warehouse/purchase/' + purchaseId,
    method: 'delete'
  })
}

// 导出采购单
export function exportPurchase(query) {
  return request({
    url: '/warehouse/purchase/export',
    method: 'post',
    data: query
  })
}

// 导入采购单
export function importPurchase(data) {
  return request({
    url: '/warehouse/purchase/importData',
    method: 'post',
    data: data
  })
}

// 下载采购单导入模板
export function importTemplate() {
  return request({
    url: '/warehouse/purchase/importTemplate',
    method: 'post'
  })
}

// 生成月度采购报表
export function generateMonthlyReport(query) {
  return request({
    url: '/warehouse/purchase/monthlyReport',
    method: 'post',
    data: query
  })
}

// 生成年度采购报表
export function generateYearlyReport(query) {
  return request({
    url: '/warehouse/purchase/yearlyReport',
    method: 'post',
    data: query
  })
}

// 获取供应商选项
export function getSupplierOptions() {
  return request({
    url: '/warehouse/supplier/options',
    method: 'get'
  })
}

// 获取项目选项
export function getProjectOptions() {
  return request({
    url: '/warehouse/project/options',
    method: 'get'
  })
}

// 获取配件类型选项
export function getPartTypeOptions() {
  return request({
    url: '/warehouse/partType/options',
    method: 'get'
  })
} 