<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="原因编码" prop="reasonCode">
        <el-input
          v-model="queryParams.reasonCode"
          placeholder="请输入原因编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="原因名称" prop="reasonName">
        <el-input
          v-model="queryParams.reasonName"
          placeholder="请输入原因名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="原因类型" prop="reasonType">
        <el-select v-model="queryParams.reasonType" placeholder="请选择原因类型" clearable>
          <el-option
            v-for="dict in dict.type.return_reason_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['warehouse:returnReason:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['warehouse:returnReason:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['warehouse:returnReason:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="returnReasonList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="退货原因ID" align="center" prop="reasonId" v-if="columns[0].visible" />
      <el-table-column label="原因编码" align="center" prop="reasonCode" v-if="columns[1].visible" />
      <el-table-column label="原因名称" align="center" prop="reasonName" v-if="columns[2].visible" />
      <el-table-column label="原因类型" align="center" prop="reasonType" v-if="columns[3].visible">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.return_reason_type" :value="scope.row.reasonType"/>
        </template>
      </el-table-column>
      <el-table-column label="原因描述" align="center" prop="description" v-if="columns[4].visible" show-overflow-tooltip />
      <el-table-column label="显示顺序" align="center" prop="sortOrder" v-if="columns[5].visible" />
      <el-table-column label="状态" align="center" prop="status" v-if="columns[6].visible">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" v-if="columns[7].visible" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['warehouse:returnReason:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['warehouse:returnReason:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改退货原因分类对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="原因编码" prop="reasonCode">
          <el-input v-model="form.reasonCode" placeholder="请输入原因编码" />
        </el-form-item>
        <el-form-item label="原因名称" prop="reasonName">
          <el-input v-model="form.reasonName" placeholder="请输入原因名称" />
        </el-form-item>
        <el-form-item label="原因类型" prop="reasonType">
          <el-select v-model="form.reasonType" placeholder="请选择原因类型">
            <el-option
              v-for="dict in dict.type.return_reason_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="原因描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入原因描述" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="sortOrder">
          <el-input-number v-model="form.sortOrder" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listReturnReason, getReturnReason, delReturnReason, addReturnReason, updateReturnReason } from "@/api/warehouse/returnReason";

export default {
  name: "ReturnReason",
  dicts: ['return_reason_type', 'sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 退货原因分类表格数据
      returnReasonList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        reasonCode: null,
        reasonName: null,
        reasonType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        reasonCode: [
          { required: true, message: "原因编码不能为空", trigger: "blur" },
          { min: 2, max: 50, message: '原因编码长度必须介于 2 和 50 之间', trigger: 'blur' }
        ],
        reasonName: [
          { required: true, message: "原因名称不能为空", trigger: "blur" },
          { min: 2, max: 100, message: '原因名称长度必须介于 2 和 100 之间', trigger: 'blur' }
        ],
        reasonType: [
          { required: true, message: "原因类型不能为空", trigger: "change" }
        ]
      },
      // 列信息
      columns: [
        { key: 0, label: `退货原因ID`, visible: false },
        { key: 1, label: `原因编码`, visible: true },
        { key: 2, label: `原因名称`, visible: true },
        { key: 3, label: `原因类型`, visible: true },
        { key: 4, label: `原因描述`, visible: true },
        { key: 5, label: `显示顺序`, visible: true },
        { key: 6, label: `状态`, visible: true },
        { key: 7, label: `备注`, visible: false }
      ]
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询退货原因分类列表 */
    getList() {
      this.loading = true;
      listReturnReason(this.queryParams).then(response => {
        this.returnReasonList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        reasonId: null,
        reasonCode: null,
        reasonName: null,
        reasonType: null,
        description: null,
        sortOrder: 0,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.reasonId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加退货原因分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const reasonId = row.reasonId || this.ids
      getReturnReason(reasonId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改退货原因分类";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.reasonId != null) {
            updateReturnReason(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addReturnReason(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const reasonIds = row.reasonId || this.ids;
      this.$modal.confirm('是否确认删除退货原因分类编号为"' + reasonIds + '"的数据项？').then(function() {
        return delReturnReason(reasonIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script> 