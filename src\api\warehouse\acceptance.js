import request from '@/utils/request'

// 查询验收入库列表
export function listAcceptance(query) {
  return request({
    url: '/warehouse/acceptance/list',
    method: 'get',
    params: query
  })
}

// 查询验收入库详情
export function getAcceptance(id) {
  return request({
    url: `/warehouse/acceptance/${id}`,
    method: 'get'
  })
}

// 新增验收入库
export function addAcceptance(data) {
  return request({
    url: '/warehouse/acceptance',
    method: 'post',
    data: data
  })
}

// 修改验收入库
export function updateAcceptance(data) {
  return request({
    url: '/warehouse/acceptance',
    method: 'put',
    data: data
  })
}

// 删除验收入库
export function delAcceptance(ids) {
  return request({
    url: `/warehouse/acceptance/${ids}`,
    method: 'delete'
  })
}

// 合格入库
export function markQualified(id) {
  return request({
    url: `/warehouse/acceptance/markQualified/${id}`,
    method: 'post'
  })
}

// 不合格退货
export function markUnqualified(id) {
  return request({
    url: `/warehouse/acceptance/markUnqualified/${id}`,
    method: 'post'
  })
}

// 批量导入（如需实现）
export function importAcceptance(data) {
  return request({
    url: '/warehouse/acceptance/importData',
    method: 'post',
    data
  })
} 