import request from '@/utils/request'

// 查询供应商列表
export function listSupplier(query) {
  return request({
    url: '/warehouse/supplier/list',
    method: 'get',
    params: query
  })
}

// 查询供应商详细
export function getSupplier(supplierId) {
  return request({
    url: '/warehouse/supplier/' + supplierId,
    method: 'get'
  })
}

// 新增供应商
export function addSupplier(data) {
  return request({
    url: '/warehouse/supplier',
    method: 'post',
    data: data
  })
}

// 修改供应商
export function updateSupplier(data) {
  return request({
    url: '/warehouse/supplier',
    method: 'put',
    data: data
  })
}

// 删除供应商
export function delSupplier(supplierId) {
  return request({
    url: '/warehouse/supplier/' + supplierId,
    method: 'delete'
  })
}

// 批量删除供应商
export function delSupplierBatch(supplierIds) {
  return request({
    url: '/warehouse/supplier/' + supplierIds,
    method: 'delete'
  })
}

// 导出供应商
export function exportSupplier(query) {
  return request({
    url: '/warehouse/supplier/export',
    method: 'post',
    data: query
  })
}

// 导入供应商
export function importSupplier(data) {
  return request({
    url: '/warehouse/supplier/importData',
    method: 'post',
    data: data
  })
}

// 下载供应商导入模板
export function importTemplate() {
  return request({
    url: '/warehouse/supplier/importTemplate',
    method: 'post'
  })
}

// 获取供应商选项（用于下拉选择）
export function getSupplierOptions() {
  return request({
    url: '/warehouse/supplier/options',
    method: 'get'
  })
} 